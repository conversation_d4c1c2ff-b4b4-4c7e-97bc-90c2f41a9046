package data

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	mockDateTime "github.com/epifi/be-common/pkg/datetime/mocks"
	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_dao"
)

// TestSalaryEstimationFlow_GetFiDataRangeFrom tests the salary estimation flow logic
func TestSalaryEstimationFlow_GetFiDataRangeFrom(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)
	mockTime := mockDateTime.NewMockTime(ctr)

	// Create a mock config for testing
	mockConf := &genconf.Config{}
	mockConf.SetSalaryEstimationDataRangeMonths(6)
	mockConf.SetFirstDataPullDurationInDays(7)

	// Create test consent
	testConsent := &caPb.Consent{
		Id:               "test-consent-1",
		ConsentRequestId: "test-consent-request-id-1",
		ActorId:          "test-actor-id-1",
	}

	type fields struct {
		consentRequestDao dao.ConsentRequestDao
		conf              *genconf.Config
		datetime          datetime.Time
	}
	type args struct {
		ctx         context.Context
		dfa         *caPb.DataFetchAttempt
		initiatedBy caEnumPb.DataFetchAttemptInitiatedBy
		consent     *caPb.Consent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		setup   func()
		want    *timestampPb.Timestamp
		wantErr bool
	}{
		{
			name: "salary estimation flow - should return 6 months from now using config",
			fields: fields{
				consentRequestDao: mockCrDao,
				conf:              mockConf,
				datetime:          mockTime,
			},
			args: args{
				ctx:         context.Background(),
				dfa:         nil, // no previous attempt
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:         "test-consent-request-id-1",
					CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
				}, nil)
				mockTime.EXPECT().Now().Return(time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC)).Times(2)
			},
			want:    timestampPb.New(time.Date(2023, 7, 15, 12, 0, 0, 0, time.UTC)), // 6 months back
			wantErr: false,
		},
		{
			name: "non-salary estimation flow - should return 7 days from now using config",
			fields: fields{
				consentRequestDao: mockCrDao,
				conf:              mockConf,
				datetime:          mockTime,
			},
			args: args{
				ctx:         context.Background(),
				dfa:         nil, // no previous attempt
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:         "test-consent-request-id-1",
					CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECT_FI_TO_FI,
				}, nil)
				mockTime.EXPECT().Now().Return(time.Date(2024, 1, 15, 12, 0, 0, 0, time.UTC)).Times(2)
			},
			want:    timestampPb.New(time.Date(2024, 1, 8, 12, 0, 0, 0, time.UTC)), // 7 days back
			wantErr: false,
		},
		{
			name: "salary estimation flow - error getting consent request",
			fields: fields{
				consentRequestDao: mockCrDao,
				conf:              mockConf,
				datetime:          mockTime,
			},
			args: args{
				ctx:         context.Background(),
				dfa:         nil,
				initiatedBy: caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER,
				consent:     testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}
			p := &ProcessorService{
				consentRequestDao: tt.fields.consentRequestDao,
				conf:              tt.fields.conf,
				datetime:          tt.fields.datetime,
			}
			got, err := p.getFiDataRangeFrom(tt.args.ctx, tt.args.dfa, tt.args.initiatedBy, tt.args.consent)
			if (err != nil) != tt.wantErr {
				t.Errorf("getFiDataRangeFrom() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got != nil && tt.want != nil {
				assert.Equal(t, tt.want.AsTime().Unix(), got.AsTime().Unix())
			}
		})
	}
}

// TestSalaryEstimationFlow_IsSalaryEstimationFlow tests the isSalaryEstimationFlow function
func TestSalaryEstimationFlow_IsSalaryEstimationFlow(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCrDao := mock_dao.NewMockConsentRequestDao(ctr)

	// Create test consent
	testConsent := &caPb.Consent{
		Id:               "test-consent-1",
		ConsentRequestId: "test-consent-request-id-1",
		ActorId:          "test-actor-id-1",
	}

	type fields struct {
		consentRequestDao dao.ConsentRequestDao
	}
	type args struct {
		ctx     context.Context
		consent *caPb.Consent
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		setup   func()
		want    bool
		wantErr bool
	}{
		{
			name: "salary estimation flow - should return true",
			fields: fields{
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:         "test-consent-request-id-1",
					CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION,
				}, nil)
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "non-salary estimation flow - should return false",
			fields: fields{
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&caPb.ConsentRequest{
					Id:         "test-consent-request-id-1",
					CaFlowName: caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECT_FI_TO_FI,
				}, nil)
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "error getting consent request - should return error",
			fields: fields{
				consentRequestDao: mockCrDao,
			},
			args: args{
				ctx:     context.Background(),
				consent: testConsent,
			},
			setup: func() {
				mockCrDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}
			p := &ProcessorService{
				consentRequestDao: tt.fields.consentRequestDao,
			}
			got, err := p.isSalaryEstimationFlow(tt.args.ctx, tt.args.consent)
			if (err != nil) != tt.wantErr {
				t.Errorf("isSalaryEstimationFlow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("isSalaryEstimationFlow() got = %v, want %v", got, tt.want)
			}
		})
	}
}
