package data

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"

	"bytes"
	"context"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	caPb "github.com/epifi/gamma/api/connected_account"
	caCoPb "github.com/epifi/gamma/api/connected_account/consumer"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caExtPb "github.com/epifi/gamma/api/connected_account/external"
	caNotiPb "github.com/epifi/gamma/api/connected_account/notification"
	aaVgPb "github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/crypto"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data/fi_types/xml_models"
	caError "github.com/epifi/gamma/connectedaccount/error"
	caHelper "github.com/epifi/gamma/connectedaccount/helper"
	"github.com/epifi/gamma/connectedaccount/metrics"
	"github.com/epifi/gamma/connectedaccount/notification"
	"github.com/epifi/gamma/pkg/feature/release"
)

type ProcessorService struct {
	vgAaClient         aaVgPb.AccountAggregatorClient
	consentDao         dao.ConsentDao
	attemptDao         dao.DataFetchAttemptDao
	attemptProcessDao  dao.DataProcessAttemptDao
	conf               *genconf.Config
	edeService         crypto.Ede
	batchProcessDao    dao.AaBatchProcessDao
	fiFactory          IFIFactory
	accDao             dao.AaAccountDao
	txnDao             dao.AaTransactionDao
	consentRequestDao  dao.ConsentRequestDao
	notifier           notification.Sender
	storeDecryptedData StoreDecryptedData
	txnExecutor        storagev2.TxnExecutor
	releaseEvaluator   release.IEvaluator
	datetime           datetime.Time
}

func NewProcessorService(edeService crypto.Ede, vgAaClient aaVgPb.AccountAggregatorClient, attemptDao dao.DataFetchAttemptDao,
	consentDao dao.ConsentDao, conf *genconf.Config, attemptProcessDao dao.DataProcessAttemptDao,
	batchProcessDao dao.AaBatchProcessDao, fiFactory IFIFactory, accDao dao.AaAccountDao, txnDao dao.AaTransactionDao,
	consentRequestDao dao.ConsentRequestDao, notifier notification.Sender,
	txnExecutor storagev2.TxnExecutor, releaseEvaluator release.IEvaluator,
	datetime datetime.Time,
) *ProcessorService {
	p := &ProcessorService{
		edeService:        edeService,
		consentDao:        consentDao,
		attemptDao:        attemptDao,
		vgAaClient:        vgAaClient,
		conf:              conf,
		attemptProcessDao: attemptProcessDao,
		batchProcessDao:   batchProcessDao,
		fiFactory:         fiFactory,
		accDao:            accDao,
		txnDao:            txnDao,
		consentRequestDao: consentRequestDao,
		notifier:          notifier,
		txnExecutor:       txnExecutor,
		releaseEvaluator:  releaseEvaluator,
		datetime:          datetime,
	}
	p.storeDecryptedData = p.Store
	return p
}

var _ Processor = &ProcessorService{}

// first create an attempt and set status as initial for data fetch
// Get key material using bouncy castle VG library and place a data fetch request to vendor
// Update attempt with session id and key material
// nolint:funlen
func (p *ProcessorService) CreateAttempt(ctx context.Context, consent *caPb.Consent, initiatedBy caEnumPb.DataFetchAttemptInitiatedBy, purpose caEnumPb.DataFetchAttemptPurpose, fetchDataForCustomUser bool) (*caPb.DataFetchAttempt, error) {
	// for each consent obtained, we need to initiate a data fetch request only if there is no non terminal
	// active data fetch attempt already for that consent id
	dfa, err := p.attemptDao.GetLatestConsentAttemptByConsentId(ctx, consent.GetId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, errors.Wrap(err, "error fetching latest consent attempt by consent id")
	}
	if purpose == caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL &&
		(dfa.GetFetchStatus() == caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_INITIAL ||
			dfa.GetFetchStatus() == caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START) {
		logger.Error(ctx, "latest data fetch attempt was backfill purpose, not creating a new dfa",
			zap.Any(logger.ATTEMPT_ID, dfa.GetId()))
		return nil, errors.New(fmt.Sprintf("latest dfa with id: %v was backfill purpose, not creating a new dfa",
			dfa.GetId()))
	}
	// check if previous attempt is in terminal state, if not, do not create a new attempt for same consent id
	if dfa != nil && !caHelper.IsAttemptInTerminalStatus(dfa.GetFetchStatus()) {
		if dfa.GetUpdatedAt().AsTime().Before(time.Now().Add(-p.conf.DataFetchAttemptStatusReconciliationThreshold())) {
			dfa.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_MANUAL_INTERVENTION
			if dfaUpdateErr := p.attemptDao.UpdateById(ctx, dfa.GetId(), dfa, []caPb.DataFetchAttemptFieldMask{
				caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); dfaUpdateErr != nil {
				return nil, errors.Wrap(dfaUpdateErr, "error updating attempt fetch status in case of reconciliation.")
			}
		} else {
			return dfa, caError.ErrAttemptAlreadyExists
		}
	}

	// removing below NextFetchAt time validation as source of truth to publish consents for daily refresh is
	// sqoop job only and since ordering of messages coming to consumer if not guaranteed, there should not to be any time validation check at consumer side
	// if purpose != caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL && time.Now().Before(consent.NextFetchAt.AsTime()){
	//	return dfa, caError.ErrConsentIneligibleForDataRefresh
	// }

	cr, getDBErr := p.consentRequestDao.Get(ctx, consent.GetConsentRequestId(), caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_AA_ENTITY)
	if getDBErr != nil {
		return nil, errors.Wrap(getDBErr, "error getting consent request from DB")
	}
	vgAaEntity, err := toVgAaEntity(cr.GetAaEntity())
	if err != nil {
		return nil, err
	}
	// generate a new uuid for txnId and fiDataRange for the request
	txnId := uuid.New().String()
	fiDataRange := &aaVgPb.FIDataRange{}
	if purpose == caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL {
		fiDataRange, err = p.getFiDataRangeForTxnBackfill(ctx, consent)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching fi data range for txn backfill")
		}
	} else {
		fiDataRange, err = p.getFiDataRange(ctx, consent, initiatedBy, fetchDataForCustomUser)
		if err != nil {
			return nil, errors.Wrap(err, "error while fetching fi data range")
		}
	}
	// create an attempt and store it in db
	var accNotificationStatusList []*caPb.AccountNotificationStatus
	for _, acc := range consent.GetAccounts().GetAccountList() {
		accNotificationStatus := &caPb.AccountNotificationStatus{
			LinkRefNumber:        acc.GetLinkRefNumber(),
			ReceivedNotification: false,
		}
		accNotificationStatusList = append(accNotificationStatusList, accNotificationStatus)
	}
	dataFetchAttempt := &caPb.DataFetchAttempt{ActorId: consent.GetActorId(), ConsentId: consent.GetConsentId(),
		TransactionId: txnId, FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_INITIAL,
		DataFetchAttemptInitiatedBy: initiatedBy,
		// data pull range
		DataRangeFrom:      fiDataRange.GetFrom(),
		DataRangeTo:        fiDataRange.GetTo(),
		ConsentReferenceId: consent.GetId(),
		Purpose:            purpose,
		FiNotificationInfo: &caPb.FiNotificationInfo{AccountNotificationStatusList: accNotificationStatusList},
	}
	resDataAttempt, createErr := p.attemptDao.Create(ctx, dataFetchAttempt)
	if createErr != nil {
		return nil, errors.Wrap(createErr, "error in creating dataFetchAttempt")
	}
	return p.placeDataRequestAndUpdateAttempt(ctx, txnId, fiDataRange, consent, resDataAttempt, vgAaEntity)
}

// Generate key pair nonce and place a FI data request
func (p *ProcessorService) placeDataRequestAndUpdateAttempt(ctx context.Context, txnId string, fiDataRange *aaVgPb.FIDataRange,
	consent *caPb.Consent, attempt *caPb.DataFetchAttempt, vgAaEntity aaVgPb.AaEntity) (*caPb.DataFetchAttempt, error) {
	// generate key material for requesting to AA
	keyMaterial, err := p.edeService.GenerateKeyPairWithNonce(ctx)
	if err != nil {
		attempt.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START
		if updErr := p.attemptDao.UpdateById(ctx, attempt.GetId(), attempt, []caPb.DataFetchAttemptFieldMask{
			caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
			return nil, errors.Wrap(updErr, "error in generating key material and DB update failed")
		}
		return nil, errors.Wrap(err, "error while generating key material")
	}
	// form request parameters to be used
	vgAaRequest, vgReqErr := getVgAaRequest(txnId, fiDataRange, consent, keyMaterial, vgAaEntity)
	if vgReqErr != nil {
		attempt.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START
		if updErr := p.attemptDao.UpdateById(ctx, attempt.GetId(), attempt, []caPb.DataFetchAttemptFieldMask{
			caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
			return nil, errors.Wrap(vgReqErr, "error in forming  vg request to OneMoney for data fetch and DB update failed")
		}
		return nil, errors.Wrap(vgReqErr, "error in forming vg request to OneMoney for data fetch")
	}
	// hit vg for getting the sessionId of the data request
	vgAaResponse, requestErr := p.vgAaClient.RequestData(ctx, vgAaRequest)
	if rpcError := epifigrpc.RPCError(vgAaResponse, requestErr); rpcError != nil {
		attempt.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED_START
		if updErr := p.attemptDao.UpdateById(ctx, attempt.GetId(), attempt, []caPb.DataFetchAttemptFieldMask{
			caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
			return nil, errors.Wrap(rpcError, "error in placing request to OneMoney for data fetch and DB update failed")
		}
		// check if RequestData returns invalid argument status
		if vgAaResponse.GetStatus().IsInvalidArgument() {
			return nil, caError.ErrInvalidArgument
		}
		return nil, errors.Wrap(rpcError, "error in placing request to OneMoney for data fetch")
	}
	attempt.SessionId = vgAaResponse.GetSessionId()
	attempt.KeyMaterial = keyMaterial
	attempt.FetchStatus = caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_PENDING
	if updErr := p.attemptDao.UpdateById(ctx, attempt.GetId(), attempt, []caPb.DataFetchAttemptFieldMask{
		caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS,
		caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_SESSION_ID,
		caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_KEY_MATERIAL}); updErr != nil {
		return nil, errors.New(fmt.Sprintf("error updating attempt for session id and key material,"+
			" attemptId : %v sessionId : %v", attempt.GetId(), vgAaResponse.GetSessionId()))
	}
	return attempt, nil
}

// Try to fetch data for a given attempt id. Will not fetch anything and return error if attempt has moved to
// terminal state or data has already been fetched for that attempt. Does not change data fetch attempt status
// and only fetches data from vendor and gives to the calling service. It is the calling service's onus to update
// attempt status to it's business logic needs.
func (p *ProcessorService) FetchData(ctx context.Context, dataAttempt *caPb.DataFetchAttempt) ([]*caPb.FI, error) {
	// check whether attempt is eligible for data fetch or not
	if caHelper.IsAttemptInTerminalStatus(dataAttempt.GetFetchStatus()) {
		return nil, caError.ErrFetchAttemptTerminalStatus
	}
	if dataAttempt.GetFetchStatus() == caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FETCHED {
		return nil, caError.ErrDataAlreadyFetched
	}
	consent, err := p.consentDao.Get(ctx, dataAttempt.GetConsentReferenceId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting consent from DB")
	}
	cr, err := p.consentRequestDao.Get(ctx, consent.GetConsentRequestId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting consent request from DB")
	}
	// get session id from the attempt model
	sessionId := dataAttempt.GetSessionId()
	vgAaEntity, err := toVgAaEntity(cr.GetAaEntity())
	if err != nil {
		return nil, err
	}
	var dataResp *aaVgPb.FetchDataResponse
	if vgAaEntity == aaVgPb.AaEntity_AA_ENTITY_AA_ONE_MONEY || !p.conf.IsQueryParamsAllowedForFetchData() {
		dataResp, err = p.vgAaClient.FetchData(ctx, &aaVgPb.FetchDataRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: caHelper.GetVendorFromAaEntity(cr.GetAaEntity())},
			SessionId: sessionId,
			AaEntity:  vgAaEntity,
		})
		if rpcError := epifigrpc.RPCError(dataResp, err); rpcError != nil {
			if dataResp.GetStatus().IsPermissionDenied() {
				return nil, caError.ErrPermissionDenied
			}
			if dataResp.GetStatus().IsInvalidArgument() {
				return nil, caError.ErrInvalidArgument
			}
			return nil, errors.Wrap(rpcError, "error while getting FI data response with entity as Onemoney")
		}
	} else {
		// since, there can be multiple accounts which belongs to multiple FIPs associated with a consent,
		// so, for a consent we are creating map of fipID to list of all the accounts and triggering a FetchData VG API call,
		// for each FIP sequentially.
		fipToLinkRefNumberMappingForConsent := generateFipToLinkRefNumberMapping(consent.GetAccounts().GetAccountList())
		for fipId, accountsList := range fipToLinkRefNumberMappingForConsent {
			// query for the session id and check if the data is ready
			dataResp, err = p.vgAaClient.FetchData(ctx, &aaVgPb.FetchDataRequest{
				Header:        &commonvgpb.RequestHeader{Vendor: caHelper.GetVendorFromAaEntity(cr.GetAaEntity())},
				SessionId:     sessionId,
				AaEntity:      vgAaEntity,
				FipId:         fipId,
				LinkRefNumber: accountsList,
			})
			if rpcError := epifigrpc.RPCError(dataResp, err); rpcError != nil {
				if dataResp.GetStatus().IsPermissionDenied() {
					return nil, caError.ErrPermissionDenied
				}
				if dataResp.GetStatus().IsInvalidArgument() {
					return nil, caError.ErrInvalidArgument
				}
				return nil, errors.Wrap(rpcError, "error while getting FI data response with entity as finvu ")
			}
		}
	}
	if dataResp == nil {
		return nil, errors.Wrap(err, "No accounts found for a given consent")
	}
	// convert Vg response to Ca service response
	resp, err := convertVgToCaFi(dataResp.GetFiList())
	if err != nil {
		return nil, errors.Wrap(err, "error converting vg response to be ca")
	}

	allAccountsFromConsents := make(map[string]int)
	allAccountsFromVgResponse := make(map[string]int)
	// Check if data for all accounts is fetched or not for this attempt. It may happen that data for one FIP was prepared
	// But other FIP was not. In this case we are allowed to call fetch data again
	for _, acc := range consent.GetAccounts().GetAccountList() {
		// check if data for this FIP is present or not in response
		present := false
		for _, re := range resp {
			if re.GetFipId() == acc.GetFipId() {
				present = true
			}
		}
		if !present {
			errMsg := "data not fetched completely yet for all FIPs in consent"
			logger.Error(ctx, errMsg, zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()))
			return nil, errors.New(errMsg)
		}
		allAccountsFromConsents[acc.GetLinkRefNumber()]++
	}

	for _, re := range resp {
		for _, data := range re.GetData() {
			allAccountsFromVgResponse[data.GetLinkRefNumber()]++
		}
	}
	// For a given consent, if from vendor gateway data is not fetched for all accounts associated with that consent, then return an error
	if !p.isDataFetchedForAllAccs(ctx, allAccountsFromConsents, allAccountsFromVgResponse, dataAttempt) {
		errMsg := "data not fetched completely yet across all accounts in FIPs in consent"
		logger.Error(ctx, errMsg, zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()))
		return nil, errors.New(errMsg)
	}
	filteredAccDataList := filterVgResp(resp, allAccountsFromConsents)
	return filteredAccDataList, nil
}

func (p *ProcessorService) GetDecryptedData(ctx context.Context, attempt *caPb.DataFetchAttempt, inputData []*caPb.FI) ([]*caPb.FI, error) {
	// check whether attempt is eligible for data decryption or not
	if caHelper.IsAttemptInTerminalStatus(attempt.GetFetchStatus()) {
		return nil, caError.ErrFetchAttemptTerminalStatus
	}
	// Attempt status is moved to failed inside data processing hence decryption is not needed
	if attempt.GetFetchStatus() == caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_DECRYPTED ||
		attempt.GetFetchStatus() == caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED {
		return nil, caError.ErrDataAlreadyDecrypted
	}
	var decryptedResp []*caPb.FI
	// Decrypt data and set in response
	for _, fi := range inputData {
		if fi.GetKeyMaterial().GetKeyExpiry().AsTime().Before(time.Now()) {
			return nil, caError.ErrExpiredKey
		}
		var decryptedDataList []*caPb.Data
		for _, data := range fi.GetData() {
			decryptedFi, decryptErr := p.edeService.DecryptData(ctx, attempt.GetKeyMaterial(), fi.GetKeyMaterial(), data.GetEncryptedFi())
			if decryptErr != nil {
				return nil, errors.Wrap(decryptErr, fmt.Sprintf(
					"error encountered in decryption for FIP : %s and linkRef: %s", fi.GetFipId(), data.GetLinkRefNumber()))
			}
			decryptedDataList = append(decryptedDataList, &caPb.Data{
				LinkRefNumber:   data.GetLinkRefNumber(),
				MaskedAccNumber: data.GetMaskedAccNumber(),
				EncryptedFi:     data.GetEncryptedFi(),
				DecryptedFi:     decryptedFi,
			})
		}
		decryptedResp = append(decryptedResp, &caPb.FI{
			FipId:       fi.GetFipId(),
			Data:        decryptedDataList,
			KeyMaterial: fi.GetKeyMaterial(),
		})
	}
	return decryptedResp, nil
}

// Process data which was fetched successfully from AA for the given attempt
// This will iterate on each block of data and try to decrypt it and store it in DB
// The corresponding data process attempt will also be created and updated in this process
//
//nolint:funlen
func (p *ProcessorService) ProcessFetchedData(ctx context.Context, dataAttempt *caPb.DataFetchAttempt, data []*caPb.FI) error {
	var processErrors []error
	var emptyErrors []error
	for _, fi := range data {
		for _, dt := range fi.GetData() {
			dpa, getDpaErr := p.attemptProcessDao.CreateOrGet(ctx, dataAttempt.GetId(), fi.GetFipId(), dt.GetLinkRefNumber())
			if getDpaErr != nil {
				logger.Error(ctx, "error creating/updating data process attempt", zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()),
					zap.String(logger.FIP_ID, fi.GetFipId()), zap.String(logger.REFERENCE_ID, dt.GetLinkRefNumber()), zap.Error(getDpaErr))
				processErrors = append(processErrors, getDpaErr)
				continue
			}
			if dpa.GetDataProcessStatus() == caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS {
				continue
			}
			if storeErr := p.storeDecryptedData(ctx, dpa, dt.GetDecryptedFi(), dataAttempt); storeErr != nil {
				if errors.Is(storeErr, caError.ErrEmptyBatchedTxns) || errors.Is(storeErr, caError.ErrLinkRefNumNotFound) {
					emptyErrors = append(emptyErrors, storeErr)
				} else {
					dpa.DataProcessStatus = caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_FAILED
					if updErr := p.attemptProcessDao.UpdateById(ctx, dpa.GetId(), dpa, []caPb.DataProcessAttemptFieldMask{
						caPb.DataProcessAttemptFieldMask_DATA_PROCESS_ATTEMPT_FIELD_MASK_DATA_PROCESS_STATUS}); updErr != nil {
						logger.Error(ctx, "error updating data process attempt status", zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()),
							zap.String(logger.FIP_ID, fi.GetFipId()), zap.String(logger.REFERENCE_ID, dt.GetLinkRefNumber()), zap.Error(updErr))
					}
					logger.Error(ctx, "error storing decrypted data", zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()),
						zap.String(logger.FIP_ID, fi.GetFipId()), zap.String(logger.REFERENCE_ID, dt.GetLinkRefNumber()), zap.Error(storeErr))
					processErrors = append(processErrors, storeErr)
					continue
				}
			}
			dpa.DataProcessStatus = caEnumPb.DataProcessStatus_DATA_PROCESS_STATUS_SUCCESS
			if updErr := p.attemptProcessDao.UpdateById(ctx, dpa.GetId(), dpa, []caPb.DataProcessAttemptFieldMask{
				caPb.DataProcessAttemptFieldMask_DATA_PROCESS_ATTEMPT_FIELD_MASK_DATA_PROCESS_STATUS}); updErr != nil {
				logger.Error(ctx, "error updating  data process attempt status", zap.String(logger.ATTEMPT_ID, dataAttempt.GetId()),
					zap.String(logger.FIP_ID, fi.GetFipId()), zap.String(logger.REFERENCE_ID, dt.GetLinkRefNumber()), zap.Error(getDpaErr))
				processErrors = append(processErrors, updErr)
			}
		}
	}
	// No errors were encountered in data processing
	if processErrors == nil {
		if updErr := p.updateEntitiesOnCompletion(ctx, dataAttempt); updErr != nil {
			return errors.Wrap(updErr, "error updating entities on attempt completion")
		}
		p.notifyUserOnAttemptCompletion(ctx, dataAttempt)
		return nil
	}
	if updErr := p.attemptDao.UpdateById(ctx, dataAttempt.GetId(), &caPb.DataFetchAttempt{FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_FAILED},
		[]caPb.DataFetchAttemptFieldMask{caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
		return errors.Wrap(updErr, "updating attempt status failed for partial run")
	}
	// update status of attempt as failed
	return errors.New("error in batch processing of data")
}

func (p *ProcessorService) updateEntitiesOnCompletion(ctx context.Context, attempt *caPb.DataFetchAttempt) error {
	txnErr := p.processTxn(ctx, func(txnCtx context.Context) error {
		consent, getErr := p.consentDao.Get(txnCtx, attempt.GetConsentReferenceId())
		if getErr != nil {
			return errors.Wrap(getErr, "cannot fetch consent using consent reference id")
		}
		// Determine if this is the first attempt for this consent
		// Check only done for user initiated data attempts
		isFirstAttempt := false
		if attempt.GetDataFetchAttemptInitiatedBy() == caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER {
			isFirstAttempt = true
		}
		// If this is the first ever attempt for this consent, next fetch at will be sooner than NextFetchInterval
		if isFirstAttempt {
			// Since a new data fetch attempt will be created immediately after in the ProcessData consumer
			// We set the next fetch at as time.Now()
			consent.NextFetchAt = timestampPb.New(time.Now())
		} else {
			if attempt.GetPurpose() != caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL {
				nextDateTime := time.Now().Add(p.conf.NextFetchInterval())
				// If next fetch interval for consent is in multiples of days (24 hours) we want the consent
				// to refresh at time of consent creation on the future days
				if math.Mod(p.conf.NextFetchInterval().Hours(), float64(24)) == 0 {
					consentCreationDateTime := consent.GetCreatedAt().AsTime()
					consent.NextFetchAt = timestampPb.New(time.Date(nextDateTime.Year(), nextDateTime.Month(), nextDateTime.Day(),
						consentCreationDateTime.Hour(), consentCreationDateTime.Minute(), consentCreationDateTime.Second(),
						consentCreationDateTime.Nanosecond(), consentCreationDateTime.Location()))
				} else {
					consent.NextFetchAt = timestampPb.New(nextDateTime)
				}
			}
		}

		consent.ConsentDataRefreshStatus = caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_COMPLETED
		if _, updErr := p.consentDao.UpdateByConsentId(txnCtx, consent, []caPb.ConsentFieldMask{
			caPb.ConsentFieldMask_CONSENT_FIELD_MASK_NEXT_FETCH_AT, caPb.ConsentFieldMask_CONSENT_FIELD_MASK_DATA_REFRESH_STATUS}); updErr != nil {
			return errors.Wrap(updErr, "error updating next fetch at for consent")
		}
		if updErr := p.attemptDao.UpdateById(txnCtx, attempt.GetId(), &caPb.DataFetchAttempt{
			FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED}, []caPb.DataFetchAttemptFieldMask{
			caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
			return errors.Wrap(updErr, "error updating data fetch status in DB")
		}
		logger.Info(ctx, "successfully updated consent and attempt entities", zap.String(logger.ATTEMPT_ID, attempt.GetId()), zap.String(logger.CONSENT_ID, consent.GetConsentId()))
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error updating entities on attempt completion", zap.String(logger.ATTEMPT_ID,
			attempt.GetId()), zap.Error(txnErr))
		return txnErr
	}
	return nil
}

func (p *ProcessorService) Store(ctx context.Context, dpa *caPb.DataProcessAttempt, decryptedData string, dataAttempt *caPb.DataFetchAttempt) error {
	// decode the base64 encoded decrypted data
	dataXml, dataXmlError := base64.StdEncoding.DecodeString(decryptedData)
	if dataXmlError != nil {
		return errors.Wrap(dataXmlError, "error while base64 decoding of data")
	}
	decoder := xml.NewDecoder(bytes.NewReader(dataXml))
	decoder.Strict = false
	var rawFiDataXml xml_models.RawFiDataXmlStruct
	dataDecoderXmlError := decoder.Decode(&rawFiDataXml)
	if dataDecoderXmlError != nil {
		return errors.Wrap(dataDecoderXmlError, "error while unmarshalling of base64 decoded account data")
	}
	if storeErr := p.storeAccountsAndCreateTxnBatches(ctx, dpa, &rawFiDataXml, dataXml, dataAttempt); storeErr != nil {
		return storeErr
	}
	return nil
}

func (p *ProcessorService) storeAccountsAndCreateTxnBatches(ctx context.Context, dpa *caPb.DataProcessAttempt, rawFiDataXml *xml_models.RawFiDataXmlStruct, dataXml []byte, dataAttempt *caPb.DataFetchAttempt) error {
	storeProcessor, factoryErr := p.fiFactory.GetStoreAccountImpl(rawFiDataXml.Type)
	if factoryErr != nil {
		return factoryErr
	}
	if storeErr := storeProcessor.StoreAccount(ctx, dpa, dataXml, dataAttempt); storeErr != nil {
		return storeErr
	}
	return nil
}

func (p *ProcessorService) UpdateNextFetchAt(ctx context.Context, attemptId string) error {
	attempt, err := p.attemptDao.GetByAttemptId(ctx, attemptId)
	if err != nil {
		return errors.Wrap(err, "cannot fetch attempt")
	}
	if attempt.GetFetchStatus() != caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED {
		return errors.New("next fetch at can only be updated for attempt with completion status")
	}
	consent, err := p.consentDao.GetByConsentId(ctx, attempt.GetConsentId())
	if err != nil {
		return errors.Wrap(err, "cannot fetch consent using consent id")
	}
	if attempt.GetPurpose() != caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL {
		// If next fetch interval for consent is in multiples of days (24 hours) we want the consent
		// to refresh at time of consent creation on the future days
		nextDateTime := time.Now().Add(p.conf.NextFetchInterval())
		if math.Mod(p.conf.NextFetchInterval().Hours(), float64(24)) == 0 {
			consentCreationDateTime := consent.GetCreatedAt().AsTime()
			consent.NextFetchAt = timestampPb.New(time.Date(nextDateTime.Year(), nextDateTime.Month(), nextDateTime.Day(),
				consentCreationDateTime.Hour(), consentCreationDateTime.Minute(), consentCreationDateTime.Second(),
				consentCreationDateTime.Nanosecond(), consentCreationDateTime.Location()))
		} else {
			consent.NextFetchAt = timestampPb.New(nextDateTime)
		}
	}
	if _, updErr := p.consentDao.UpdateByConsentId(ctx, consent, []caPb.ConsentFieldMask{
		caPb.ConsentFieldMask_CONSENT_FIELD_MASK_NEXT_FETCH_AT}); updErr != nil {
		return errors.Wrap(updErr, "error updating next fetch at for consent")
	}
	return nil
}

func (p *ProcessorService) StoreBatch(ctx context.Context, batch *caPb.BatchProcessTransaction, req *caCoPb.ProcessTransactionsBatchRequest) error {
	eligible, err := p.isBatchEligibleForProcessing(ctx, batch)
	if err != nil {
		return errors.Wrap(err, "error determining if batch is eligible for processing")
	}
	if !eligible {
		return caError.ErrBatchIneligibleForProcessing
	}
	txnProcessor, factoryErr := p.fiFactory.GetStoreTxnBatchImpl(req.GetFiType())
	if factoryErr != nil {

		return factoryErr
	}
	// Get account from DB
	acc, err := p.accDao.GetById(ctx, req.GetAccountReferenceId(), []caPb.AaAccountFieldMask{})
	if err != nil {
		return errors.Wrap(err, "error getting account by id")
	}
	if batchErr := txnProcessor.StoreTxnBatch(ctx, req, acc.GetFipId(), caEnumPb.TransactionEventTypeInitiatedBy_TRANSACTION_EVENT_TYPE_INITIATED_BY_PERIODIC_DATA_PULL_TXNS); batchErr != nil {
		return errors.Wrap(batchErr, fmt.Sprintf("error in processing batch for : %v", req.GetFiType()))
	}
	txnErr := p.updateBatchAttemptAndNextFetch(ctx, batch)
	if txnErr != nil {
		return txnErr
	}
	return nil
}

func (p *ProcessorService) updateBatchAttemptAndNextFetch(ctx context.Context, batch *caPb.BatchProcessTransaction) error {
	txnErr := p.processTxn(ctx, func(txnCtx context.Context) error {
		if updErr := p.batchProcessDao.UpdateBatchById(txnCtx, batch.GetId(), &caPb.BatchProcessTransaction{
			Status: caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS}, []caPb.AaBatchProcessFieldMask{
			caPb.AaBatchProcessFieldMask_AA_BATCH_PROCESS_FIELD_MASK_STATUS}); updErr != nil {
			return errors.Wrap(updErr, "batch was processed but updating status to success failed")
		}
		// If this was the last batch which was successfully processed, update status of attempt also
		_, getBatchErr := p.batchProcessDao.GetByFetchAttemptProcessAttemptBatchNum(txnCtx, batch.GetFetchAttemptId(),
			batch.GetProcessAttemptId(), batch.GetBatchNumber()+1)
		if getBatchErr != nil {
			if !errors.Is(getBatchErr, epifierrors.ErrRecordNotFound) {
				return errors.Wrap(getBatchErr, "error determining if this was last batch in attempt")
			}
			if updErr := p.attemptDao.UpdateById(txnCtx, batch.GetFetchAttemptId(), &caPb.DataFetchAttempt{
				FetchStatus: caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED}, []caPb.DataFetchAttemptFieldMask{
				caPb.DataFetchAttemptFieldMask_DATA_FETCH_ATTEMPT_FIELD_MASK_FETCH_STATUS}); updErr != nil {
				return errors.Wrap(updErr, "updating attempt status failed for last batch processed")
			}
			// Update next fetch at only if last batch
			if nextFetchErr := p.UpdateNextFetchAt(txnCtx, batch.GetFetchAttemptId()); nextFetchErr != nil {
				return errors.Wrap(nextFetchErr, "error updating next fetch at")
			}
		}
		return nil
	})
	return txnErr
}

func (p *ProcessorService) isBatchEligibleForProcessing(ctx context.Context, curBatch *caPb.BatchProcessTransaction) (bool, error) {
	// Get current batch from DB
	if curBatch.GetStatus() == caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS {
		return false, nil
	}
	// check if previous batch has successfully processed or not
	prevBatch, err := p.batchProcessDao.GetByFetchAttemptProcessAttemptBatchNum(ctx, curBatch.GetFetchAttemptId(),
		curBatch.GetProcessAttemptId(), curBatch.GetBatchNumber()-1)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return false, errors.Wrap(err, "error getting previous batch from db")
	}
	// If record not found error, it means current batch is the first batch in the list and we need to process it
	if prevBatch != nil && prevBatch.GetStatus() != caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS {
		return false, nil
	}
	return true, nil
}

// Get next data range to fetch data based on previous attempt's success failure status
// In case previous attempt was success we fetch the next data incrementally
func (p *ProcessorService) getFiDataRange(ctx context.Context, consent *caPb.Consent, initiatedBy caEnumPb.DataFetchAttemptInitiatedBy, fetchDataForCustomUser bool) (*aaVgPb.FIDataRange, error) {
	// Get the latest success attempt by consent id which have been initiated by job
	// This is because user initiated data attempts will always be dependent -
	// on the data range of job initiated data attempts and not other user initiated data attempts
	dfa, err := p.attemptDao.GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(ctx, consent.GetId(),
		caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_PERIODIC,
			caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_UNSPECIFIED})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}
	fiDataRange := &aaVgPb.FIDataRange{
		To: timestampPb.New(p.datetime.Now()),
	}
	from, err := p.getFiDataRangeFrom(ctx, dfa, initiatedBy, consent)
	if err != nil {
		return nil, errors.Wrap(err, "error getting FI data range from")
	}
	fiDataRange.From = from
	// Adding an overlap to the fi data range which was generated using the previous successful fetch attempt
	// Do not add overlap if this is the first data fetch attempt
	fromTimeWithOverlap := fiDataRange.From.AsTime()
	// if this is the first ever data pull for the user, do not add overlap to it
	if !(dfa == nil && initiatedBy == caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER) {
		fromTimeWithOverlap = fiDataRange.GetFrom().AsTime().Add(-p.conf.FiDataRangeOverlapInterval())
		if fetchDataForCustomUser {
			fromTimeWithOverlap = fiDataRange.GetFrom().AsTime().Add(-p.conf.FiDataRangeOverlapIntervalForCustomfetch())
		}
	}
	fiDataRange.From = timestampPb.New(fromTimeWithOverlap)
	// From and To should be in bounds of from and to of consent FI data range
	if fiDataRange.GetFrom().AsTime().Before(consent.GetDataRangeFrom().AsTime()) {
		fiDataRange.From = consent.GetDataRangeFrom()
	}
	// From should not be before DataRangeStartYears
	// This can happen if last successful data fetch attempt is too old(earlier than DataRangeStartYears)
	earliestPossibleFromTime := p.datetime.Now().AddDate(p.conf.Consent().DataRangeStartYears(), 0, 0)
	if fiDataRange.GetFrom().AsTime().Before(earliestPossibleFromTime) {
		fiDataRange.From = timestampPb.New(earliestPossibleFromTime)
	}
	return fiDataRange, nil
}

func (p *ProcessorService) getFiDataRangeForTxnBackfill(ctx context.Context, consent *caPb.Consent) (*aaVgPb.FIDataRange, error) {
	// Get the latest successful attempt(i.e. where fetch status is marked Completed) for a consent ID given its initiator and purpose
	dfa, err := p.attemptDao.GetLatestSuccessAttemptByConsentIdAndInitiatorAndPurpose(ctx, consent.GetId(),
		caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB,
		[]caEnumPb.DataFetchAttemptPurpose{caEnumPb.DataFetchAttemptPurpose_DATA_FETCH_ATTEMPT_PURPOSE_BACKFILL})
	// If received Record not found error then it will due to no attempt with purpose as Backfill,
	// Idea here is that keep finding data with range current time to current time - 1 year in reverse order
	// until the FROM time is not lesser than consent creation - 1 year.
	// example timeline (in reverse order), consent creation date is 6th Jun 2021 and cut of date is 24th Jan 2023
	// 24th Jan'23 -> 24th Jan'22
	// 24th Jan'22 -> 24th Jan'21
	// 24th Jan'21 -> ❌24th Jan'20 ,✅6th Jun 2020 [but from date i.e. 24th Jan'20 is less than 6th Jun 2020 (consent creation - 1 year), so take from date as 6th Jun 2020]
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			txnBackfillCutOffDate, txnBackfillCutOffDateErr := time.Parse(time.RFC3339, p.conf.TxnBackfillCutOffDate())
			if txnBackfillCutOffDateErr != nil {
				return nil, txnBackfillCutOffDateErr
			}
			return p.computeFiDataRangeForTxnBackfillHelper(txnBackfillCutOffDate, consent.GetCreatedAt().AsTime()), nil
		} else {
			return nil, err
		}
	}
	return p.computeFiDataRangeForTxnBackfillHelper(dfa.GetDataRangeFrom().AsTime(), consent.GetCreatedAt().AsTime()), nil
}

func (p *ProcessorService) computeFiDataRangeForTxnBackfillHelper(toTimeDataRangeVal, consentCreationDate time.Time) *aaVgPb.FIDataRange {
	fiDataRangeForBackfill := &aaVgPb.FIDataRange{
		To:   timestampPb.New(toTimeDataRangeVal),
		From: timestampPb.New(toTimeDataRangeVal.AddDate(-1, 0, 0)),
	}
	if fiDataRangeForBackfill.GetFrom().AsTime().Before(consentCreationDate.AddDate(-1, 0, 0)) {
		fiDataRangeForBackfill.From = timestampPb.New(consentCreationDate.AddDate(-1, 0, 0))
	}

	return fiDataRangeForBackfill
}

func (p *ProcessorService) getFiDataRangeFrom(ctx context.Context, dfa *caPb.DataFetchAttempt, initiatedBy caEnumPb.DataFetchAttemptInitiatedBy, consent *caPb.Consent) (*timestampPb.Timestamp, error) {
	// If data fetch attempt initiated by job is already present -
	// data range to of the older attempt should be the data range from for the new data fetch attempt
	if dfa != nil {
		return dfa.GetDataRangeTo(), nil
	}
	// If no data fetch attempts by job are present, we need to check if this is the first data pull or not
	// If the data fetch is initiated by the user - it is the first data pull
	// If the data fetch is initiated by job, then we need to complete pulling older data as well

	// default dataRangeStartYears is consent creationDate-1 year, due to limitation at bank side, 1 year data pull may fail,
	// but 6 months(say) may pass, so keeping DataRangeStartYears bank specific
	// check
	fipSpecificDataRangeStartTime := p.datetime.Now().AddDate(p.conf.Consent().DataRangeStartYears(), 0, 0).Add(48 * time.Hour)
	// one consent may have multiple accounts belonging to different FIPs
	// take minimum of DataRangeStartDuration across all FIPs in consent, currently one consent will have accounts from same FIP.
	// TODO(@mayank): change this minimum function with return appropriate DataRangeStartDuration once consent will have accounts from different FIPs
	for _, acc := range consent.GetAccounts().GetAccountList() {
		fipControlConfigs, defined := p.conf.FipLevelControl()[acc.GetFipId()]
		if defined && fipControlConfigs != nil && fipControlConfigs.DataRangeStartDuration.Seconds() != float64(0) {
			currFipSpecificDataRangeStartTime := p.datetime.Now().Add(-1 * fipControlConfigs.DataRangeStartDuration)
			if currFipSpecificDataRangeStartTime.After(fipSpecificDataRangeStartTime) {
				fipSpecificDataRangeStartTime = currFipSpecificDataRangeStartTime
			}
		}
	}
	switch initiatedBy {
	case caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER:
		// Check if this is salary-estimation flow to return 6 months instead of default duration
		salaryEstimationFlow, err := p.isSalaryEstimationFlow(ctx, consent)
		if err != nil {
			return nil, errors.Wrap(err, "error checking if salary estimation flow")
		}
		if salaryEstimationFlow {
			return timestampPb.New(p.datetime.Now().AddDate(0, -6, 0)), nil // 6 months from now
		}
		return timestampPb.New(p.datetime.Now().AddDate(0, 0, -p.conf.FirstDataPullDurationInDays())), nil
	case caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_JOB:
		return timestampPb.New(fipSpecificDataRangeStartTime), nil
	default:
		// return default if initiator is not mentioned
		return timestampPb.New(p.datetime.Now().AddDate(p.conf.Consent().DataRangeStartYears(), 0, 0)), nil
	}
}

// GetAccountDetails is used to fetch account details, profile details and summary of a given accountId
// summary here is returned as an interface since it can any of the FI type i.e DEPOSIT, RECURRING_DEPOSIT, TERM_DEPOSIT
// service layers calling this function have the responsibility to convert the summary response into appropriate FI response type as per their needs
func (p *ProcessorService) GetAccountDetails(ctx context.Context, accountId string, accDetailsMask []caExtPb.AccountDetailsMask) (*caExtPb.AccountDetails, interface{}, interface{}, error) {
	isProfileRequired := false
	var selectFieldMask []caPb.AaAccountFieldMask
	for _, detailsMaks := range accDetailsMask {
		if detailsMaks == caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE {
			selectFieldMask = append(selectFieldMask, caPb.AaAccountFieldMask_AA_ACCOUNT_FIELD_MASK_PROFILE)
			isProfileRequired = true
		}
	}
	// get account details related to account_id
	accRes, accErr := p.accDao.GetById(ctx, accountId, selectFieldMask)
	if accErr != nil {
		return nil, nil, nil, errors.Wrap(accErr, "error in getting account details")
	}
	// converting to external account details proto
	accountDetails := accRes.ConvertToExternalAccountDetail(p.conf)
	var profileDetails interface{}
	// if profile mask is present in request, convert the profile to external profile details
	if isProfileRequired {
		var profileResErr error
		// get profile implementation of specific FI type of the account
		profileProcessor, factoryErr := p.fiFactory.GetProfileDetailsImpl(accRes.GetAccInstrumentType())
		if factoryErr != nil {
			return nil, nil, nil, errors.Wrap(factoryErr, "error in getting profile details implementation")
		}
		// fetching profile details
		profileDetails, profileResErr = profileProcessor.GetProfileDetails(ctx, accountId)
		if profileResErr != nil {
			if !errors.Is(profileResErr, epifierrors.ErrRecordNotFound) {
				return nil, nil, nil, errors.Wrap(profileResErr, "error fetching profile details")
			}
		}
	}
	// get summary implementation of specific FI type of the account
	summaryProcessor, factoryErr := p.fiFactory.GetSummaryDetailsImpl(accRes.GetAccInstrumentType())
	if factoryErr != nil {
		return nil, nil, nil, errors.Wrap(factoryErr, "error in getting summary details implementation")
	}
	// fetching summary details
	summaryRes, summaryErr := summaryProcessor.GetSummaryDetails(ctx, accountId)
	if summaryErr != nil {
		if errors.Is(summaryErr, epifierrors.ErrRecordNotFound) {
			return accountDetails, profileDetails, nil, nil
		}
		return nil, nil, nil, errors.Wrap(summaryErr, "error fetching summary details")
	}
	return accountDetails, profileDetails, summaryRes, nil
}

func (p *ProcessorService) GetAccountDetailsBulk(ctx context.Context, accountIdList []string, accDetailsMask []caExtPb.AccountDetailsMask) (map[string]*caPb.AccountProfileSummaryDetails, error) {
	res := make(map[string]*caPb.AccountProfileSummaryDetails)
	isProfileRequired := false
	for _, detailsMask := range accDetailsMask {
		if detailsMask == caExtPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_PROFILE {
			isProfileRequired = true
			break
		}
	}
	// get bulk account details
	accResList, accErr := p.accDao.GetBulkById(ctx, accountIdList)
	if accErr != nil {
		if errors.Is(epifierrors.ErrRecordNotFound, accErr) {
			return nil, accErr
		}
		return nil, errors.Wrap(accErr, "error in getting account details")
	}

	// make a map where key is fi type and value is a list of account ids belonging to the fi type
	// this will be used later while bulk fetching summary details
	fiTypeMap := make(map[string][]string)
	for _, accRes := range accResList {
		// converting to external account details proto
		accountDetails := accRes.ConvertToExternalAccountDetail(p.conf)

		var profileDetails *caExtPb.ProfileDetails
		// if profile mask is present in request, convert the profile to external profile details
		if isProfileRequired {
			profileDetails = convertToExtProfileDetails(accRes.GetProfile())
		}

		res[accRes.GetId()] = &caPb.AccountProfileSummaryDetails{AccountDetails: accountDetails, ProfileDetails: profileDetails}

		// add account ids to fiTypeMap
		fiTypeMap[accountDetails.GetAccInstrumentType().String()] = append(fiTypeMap[accountDetails.GetAccInstrumentType().String()], accountDetails.GetAccountId())
	}

	p.populateFipMeta(ctx, res, accResList)
	// get bulk summary for all accounts in the fiTypeMap
	bulkSummary := p.GetBulkSummary(ctx, fiTypeMap)
	p.populateIfscCodes(ctx, res, bulkSummary)

	// get bulk profile and set in response
	bulkProfile := p.GetBulkProfile(ctx, fiTypeMap)
	for accId, accProfile := range bulkProfile {
		switch res[accId].GetAccountDetails().GetAccInstrumentType() {
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_DepositProfile{DepositProfile: accProfile.(*caExtPb.HoldersDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_TermDepositProfile{TermDepositProfile: accProfile.(*caExtPb.HoldersDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_RecurringDepositProfile{RecurringDepositProfile: accProfile.(*caExtPb.HoldersDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_EquityProfile{EquityProfile: accProfile.(*caExtPb.EquityProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_EtfProfile{EtfProfile: accProfile.(*caExtPb.EtfProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_ReitProfile{ReitProfile: accProfile.(*caExtPb.ReitProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_InvitProfile{InvitProfile: accProfile.(*caExtPb.InvitProfileDetails)}
		case caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_NPS:
			res[accId].Profile = &caPb.AccountProfileSummaryDetails_NpsProfileDetails{NpsProfileDetails: accProfile.(*caExtPb.NpsProfileDetails)}
		default:
			logger.Error(ctx, "GetAccountDetailsBulk: invalid accInstType or profile details are null or profile mask is not present in request", zap.String(logger.ACCOUNT_ID, accId), zap.Any("accDetailsMask", accDetailsMask),
				zap.Any("accInstType", res[accId].GetAccountDetails().GetAccInstrumentType()))
		}
	}
	return res, nil
}

// fills up summary and ifsc codes
func (p *ProcessorService) populateIfscCodes(ctx context.Context, res map[string]*caPb.AccountProfileSummaryDetails, bulkSummary map[string]interface{}) {
	for accId, summaryDetails := range bulkSummary {
		switch summary := summaryDetails.(type) {
		case *caExtPb.DepositSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_DepositSummary{DepositSummary: summary}
			res[accId].GetAccountDetails().IfscCode = summary.GetIfscCode()
		case *caExtPb.RecurringDepositSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_RecurringDepositSummary{RecurringDepositSummary: summary}
			res[accId].GetAccountDetails().IfscCode = summary.GetIfscCode()
		case *caExtPb.TermDepositSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_TermDepositSummary{TermDepositSummary: summary}
			res[accId].GetAccountDetails().IfscCode = summary.GetIfscCode()
		case *caExtPb.EquitySummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_EquitySummary{EquitySummary: summary}
		case *caExtPb.EtfSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_EtfSummary{EtfSummary: summary}
		case *caExtPb.ReitSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_ReitSummary{ReitSummary: summary}
		case *caExtPb.InvitSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_InvitSummary{InvitSummary: summary}
		case *caExtPb.NpsSummary:
			res[accId].Summary = &caPb.AccountProfileSummaryDetails_NpsSummary{NpsSummary: summary}
		default:
			logger.Info(ctx, "bulk summary obtained does not match existing type, not populating", zap.String(logger.ACCOUNT_ID, accId))
		}
	}
}

// fills up fip meta
func (p *ProcessorService) populateFipMeta(ctx context.Context, res map[string]*caPb.AccountProfileSummaryDetails, accList []*caPb.AaAccount) {
	for _, acc := range accList {
		accId := acc.GetId()
		// populate fip meta once ifsc codes have been populated
		fipMeta, err := p.conf.GetFipMetaByFipId(acc.GetFipId())
		if err != nil {
			logger.Error(ctx, "error fetching fip meta, not populating", zap.String(logger.ACCOUNT_ID, accId), zap.String(logger.FIP_ID, res[accId].GetAccountDetails().GetFipId()), zap.Error(err))
			continue
		}
		// Enrich meta if jupiter account
		logoUrl, displayName := fipMeta.LogoUrl, fipMeta.DisplayName
		if res[accId].GetAccountDetails().GetIfscCode() == p.conf.JupiterMetaData().IfscCode() {
			logoUrl, displayName = p.conf.JupiterMetaData().LogoUrl(), p.conf.JupiterMetaData().DisplayName()
		}
		res[accId].GetAccountDetails().FipMeta = &caExtPb.FipMeta{
			FipId:       fipMeta.GetFipId(),
			Bank:        fipMeta.GetBank(),
			Name:        fipMeta.GetName(),
			LogoUrl:     logoUrl,
			DisplayName: displayName,
		}
	}
}

func (p *ProcessorService) GetBulkSummary(ctx context.Context, fiTypeMap map[string][]string) map[string]interface{} {
	// Create an array to store map[string]interface{} for all fi types
	// Key of the map is account id and interface{} is the summary
	// We later combine the maps for all fi types into one
	var combinedSummaries []map[string]interface{}
	for fiTypeString, accountIdList := range fiTypeMap {
		// get implementation for fi type
		summaryProcessor, factoryErr := p.fiFactory.GetSummaryDetailsImpl(caEnumPb.AccInstrumentType(caEnumPb.AccInstrumentType_value[fiTypeString]))
		if factoryErr != nil {
			logger.Error(ctx, "error in getting summary details implementation", zap.String("fiType", fiTypeString), zap.Error(factoryErr))
			continue
		}
		// get bulk details
		bulkSummary, bsErr := summaryProcessor.GetSummaryDetailsBulk(ctx, accountIdList)
		if bsErr != nil {
			logger.Error(ctx, "error in getting bulk summary", zap.Error(bsErr))
			continue
		}
		combinedSummaries = append(combinedSummaries, bulkSummary)
	}

	// combine all summaries into one single map
	res := make(map[string]interface{})
	for _, summaries := range combinedSummaries {
		for accId, summary := range summaries {
			res[accId] = summary
		}
	}
	return res
}

// nolint : dupl
func (p *ProcessorService) GetBulkProfile(ctx context.Context, fiTypeMap map[string][]string) map[string]interface{} {
	// Create an array to store map[string]interface{} for all fi types
	// Key of the map is account id and interface{} is the profile
	// We later combine the maps for all fi types into one
	var combinedProfiles []map[string]interface{}
	for fiTypeString, accountIdList := range fiTypeMap {
		// get implementation for fi type
		profileProcessor, factoryErr := p.fiFactory.GetProfileDetailsImpl(caEnumPb.AccInstrumentType(caEnumPb.AccInstrumentType_value[fiTypeString]))
		if factoryErr != nil {
			logger.Error(ctx, "error in getting profile details implementation", zap.String("fiType", fiTypeString), zap.Error(factoryErr))
			continue
		}
		// get bulk details
		bulkProfile, bulkProfileErr := profileProcessor.GetProfileDetailsBulk(ctx, accountIdList)
		if bulkProfileErr != nil {
			logger.Error(ctx, "error in getting bulk profile", zap.Error(bulkProfileErr))
			continue
		}
		combinedProfiles = append(combinedProfiles, bulkProfile)
	}

	// combine all profiles into one single map
	accIdToProfileMap := make(map[string]interface{})
	for _, profiles := range combinedProfiles {
		for accId, profile := range profiles {
			accIdToProfileMap[accId] = profile
		}
	}

	return accIdToProfileMap

}

func (p *ProcessorService) notifyUserOnAttemptCompletion(ctx context.Context, attempt *caPb.DataFetchAttempt) {
	isFirstDataAttemptForConsent := attempt.GetDataFetchAttemptInitiatedBy() == caEnumPb.DataFetchAttemptInitiatedBy_DATA_FETCH_ATTEMPT_INITIATED_BY_USER
	if isFirstDataAttemptForConsent {
		att, err := p.attemptDao.GetByAttemptId(ctx, attempt.GetId())
		if err != nil {
			logger.Error(ctx, "error getting attempt from db", zap.Error(err))
			return
		}
		// If attempt has completed before threshold do not send notification
		if !att.GetUpdatedAt().AsTime().After(att.CreatedAt.AsTime().Add(p.conf.AttemptCompletionThreshold())) {
			return
		}
		consent, getErr := p.consentDao.Get(ctx, att.GetConsentReferenceId())
		if getErr != nil {
			logger.Error(ctx, "error getting consent from db", zap.Error(err))
			return
		}
		// If consent is not active, do not send notification
		if consent.GetConsentStatus() == caEnumPb.ConsentStatus_CONSENT_STATUS_DELETED_EPIFI {
			return
		}
		consentRequest, crErr := p.consentRequestDao.Get(ctx, consent.GetConsentRequestId(),
			caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_CA_FLOW_NAME)
		if crErr != nil {
			logger.Error(ctx, "cannot fetch consent request using consent_request_id",
				zap.String(logger.CONSENT_REQUEST_ID, consent.GetConsentRequestId()), zap.Error(crErr))
			return
		}
		if consentRequest.GetCaFlowName() != caEnumPb.CAFlowName_CA_FLOW_NAME_CONNECT_FI_TO_FI {
			sendErr := p.notifier.Send(ctx, &caNotiPb.CaComms{
				CommsType: caEnumPb.CommsType_COMMS_TYPE_FIRST_DATA_PULL_SUCCESS,
				CommsParams: &caNotiPb.CaComms_FirstDataPullSuccessParams{FirstDataPullSuccessParams: &caNotiPb.FirstDataPullSuccessParams{
					Consent: consent,
					Attempt: att,
				}},
				ActorId: att.GetActorId(),
			})
			if sendErr != nil {
				metrics.RecordUserNotificationError(caEnumPb.CommsType_COMMS_TYPE_FIRST_DATA_PULL_SUCCESS)
				logger.Error(ctx, "error determining if attempt is first success attempt for consent", zap.Error(sendErr))
				return
			}
		}
	}
}

func (p *ProcessorService) isSalaryEstimationFlow(ctx context.Context, consent *caPb.Consent) (bool, error) {
	consentRequest, err := p.consentRequestDao.Get(ctx, consent.GetConsentRequestId(),
		caPb.ConsentRequestFieldMask_CONSENT_REQUEST_FIELD_MASK_CA_FLOW_NAME)
	if err != nil {
		return false, errors.Wrapf(err, "error getting consent request by id: %s", consent.GetConsentRequestId())
	}
	return consentRequest.GetCaFlowName() == caEnumPb.CAFlowName_CA_FLOW_NAME_SALARY_ESTIMATION, nil
}

func (p *ProcessorService) processTxn(ctx context.Context, inTransactionMethodImpl storagev2.InTransaction) error {
	return p.txnExecutor.RunTxn(ctx, inTransactionMethodImpl)
}
